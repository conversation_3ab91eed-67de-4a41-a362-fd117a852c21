"""
Main application entry point for AI RAG Complaint Classification System.

This module demonstrates the complete AI-powered complaint classification system
with the following capabilities:

1. Clean Architecture implementation with separation of concerns
2. Intent detection and content summarization using Agno framework
3. RAG similarity search with Elasticsearch vector storage
4. Multi-LLM classification support (OpenAI, Anthropic, Google)
5. Batch processing with configurable concurrency
6. Comprehensive error handling and fallback mechanisms

Usage:
    uv run src/main.py

Environment Variables Required:
    GEMINI_API_KEY - Google Gemini API key
    OPENAI_API_KEY - OpenAI API key (optional)
    ANTHROPIC_API_KEY - Anthropic API key (optional)
    ELASTICSEARCH_URL - Elasticsearch server URL
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path for imports
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv

from src.application.use_cases.classify_complaints import ClassifyComplaintsUseCase
from src.application.use_cases.detect_intents import DetectIntentsUseCase
from src.application.use_cases.similarity_search import SimilaritySearchUseCase
from src.config import Settings, get_config_manager
from src.domain.entities.complaint_input import ComplaintInput
from src.domain.entities.confidence_level import ConfidenceLevel
from src.infrastructure.embedders.gemini_embedder import GeminiEmbedder
from src.infrastructure.llm_classification.agno_llm_classifier import (
    AgnoLLMClassificationService,
)
from src.infrastructure.monitoring.classification_metrics import ClassificationMetrics
from src.infrastructure.rag_elasticsearch_retriever import RAGElasticsearchRetriever
from src.infrastructure.repositories.elasticsearch_repository import (
    ElasticsearchRepository,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def setup_system(settings: Settings):
    """
    Set up the complete AI RAG system with all components.

    Returns:
        Tuple of (orchestrator, repository, embedder)
    """

    logger.info("🚀 Initializing AI RAG Complaint Classification System")

    # Initialize core infrastructure components
    logger.info("📦 Setting up infrastructure components...")

    # Embedder for processing categories (data ingestion)
    embedder = GeminiEmbedder(settings)

    # Elasticsearch repository for vector storage
    repository = ElasticsearchRepository(settings)

    # RAG retriever for similarity search
    rag_retriever = RAGElasticsearchRetriever(settings, embedder)

    # Initialize application use cases
    logger.info("🔧 Configuring application use cases...")

    # Get configuration manager for centralized config management
    config_manager = get_config_manager()

    # Intent detection use case with new config system
    intent_config = config_manager.get_intent_detection_config()
    detect_intents_use_case = DetectIntentsUseCase(intent_config)

    # Similarity search use case
    similarity_search_use_case = SimilaritySearchUseCase(rag_retriever, settings)

    # Initialize classification service and metrics with new config system
    classification_config = config_manager.get_classification_config()
    classification_service = AgnoLLMClassificationService(classification_config)
    metrics = ClassificationMetrics()

    # Classification orchestrator
    classification_orchestrator = ClassifyComplaintsUseCase(
        detect_intents_use_case=detect_intents_use_case,
        similarity_search_use_case=similarity_search_use_case,
        classification_service=classification_service,
        metrics=metrics,
        enable_llm_analysis=classification_config.enable_llm_analysis,
    )

    logger.info(
        f"✅ System initialized with LLM provider: {classification_config.llm_provider}"
    )

    return classification_orchestrator, repository, embedder


async def demo_classification_workflow(orchestrator: ClassifyComplaintsUseCase):
    """Demonstrate the complete classification workflow with sample data."""

    logger.info("\n" + "=" * 60)
    logger.info("🎯 CLASSIFICATION WORKFLOW DEMONSTRATION")
    logger.info("=" * 60)

    # Sample complaints for demonstration
    sample_complaints = [
        ComplaintInput(
            case_id="MAIN-001",
            subject="交通號誌故障",
            content="台北市信義區基隆路與忠孝東路口的紅綠燈故障了，造成交通大亂，請儘速派人維修。",
        ),
        ComplaintInput(
            case_id="MAIN-002",
            subject="路面坑洞問題",
            content="新北市板橋區中山路二段有很大的坑洞，機車經過很容易摔倒，請盡快修補。",
        ),
        ComplaintInput(
            case_id="MAIN-003",
            subject="噪音污染檢舉",
            content="隔壁工廠晚上施工噪音太大，影響居民睡眠，請環保局來檢查。",
        ),
    ]

    logger.info(f"📝 Processing {len(sample_complaints)} sample complaints...")

    try:
        # Single complaint demonstration
        logger.info("\n🔍 Single Complaint Classification:")
        result = await orchestrator.execute(sample_complaints[0])

        logger.info(f"   Case ID: {result.case_id}")
        logger.info(f"   Category: {result.main_category} > {result.sub_category}")
        logger.info(f"   Confidence: {result.confidence}")
        logger.info(f"   Similarity Score: {result.similarity_score:.3f}")
        logger.info(f"   Intents: {len(result.intents)} detected")
        logger.info(f"   Processing Time: {result.processing_time_ms}ms")

        # Batch processing demonstration
        logger.info(f"\n📦 Batch Processing ({len(sample_complaints)} complaints):")
        batch_results = await orchestrator.execute_batch(
            sample_complaints, concurrency_limit=2
        )

        # Summary statistics
        high_confidence = sum(
            1 for r in batch_results if r.confidence == ConfidenceLevel.HIGH
        )
        avg_processing_time = sum(
            r.processing_time_ms or 0 for r in batch_results
        ) / len(batch_results)

        logger.info(f"   Successfully processed: {len(batch_results)} complaints")
        logger.info(
            f"   High confidence results: {high_confidence}/{len(batch_results)} ({high_confidence / len(batch_results) * 100:.1f}%)"
        )
        logger.info(f"   Average processing time: {avg_processing_time:.0f}ms")

        # Performance metrics
        logger.info("\n📊 Performance Metrics:")
        metrics = orchestrator.get_performance_metrics()
        if metrics.get("status") != "no_data":
            logger.info(f"   Total processed: {metrics['total_processed']}")
            logger.info(f"   Success rate: {metrics['success_rate'] * 100:.1f}%")
            logger.info(f"   Average total time: {metrics['avg_total_time_ms']:.0f}ms")
            logger.info(
                f"   Average intent time: {metrics['avg_intent_time_ms']:.0f}ms"
            )
            logger.info(f"   Average RAG time: {metrics['avg_rag_time_ms']:.0f}ms")
            logger.info(f"   Average LLM time: {metrics['avg_llm_time_ms']:.0f}ms")

        return True

    except Exception as e:
        logger.error(f"❌ Classification demonstration failed: {e}")
        return False


async def demo_data_processing(
    repository: ElasticsearchRepository, embedder: GeminiEmbedder
):
    """Demonstrate data processing and ingestion (if needed)."""

    logger.info("\n" + "=" * 60)
    logger.info("📚 DATA PROCESSING DEMONSTRATION")
    logger.info("=" * 60)

    try:
        logger.info(
            "✅ Data processing demonstration - using existing repository setup"
        )
        logger.info("   Classification system is ready to process complaints")
        return True

    except Exception as e:
        logger.error(f"❌ Data processing check failed: {e}")
        return False


async def main():
    """Main application entry point."""

    print("\n🚀 AI RAG 台灣政府陳情分類系統")
    print("=" * 80)
    print("Clean Architecture + Multi-LLM + RAG Search + Intent Detection")
    print("=" * 80)

    try:
        # Load configuration
        logger.info("⚙️  Loading system configuration...")
        settings = Settings()
        config_manager = get_config_manager()
        classification_config = config_manager.get_classification_config()

        logger.info(f"   Environment: {settings.environment}")
        logger.info(f"   Elasticsearch: {settings.elasticsearch_url}")
        logger.info(f"   LLM Provider: {classification_config.llm_provider}")
        logger.info(
            f"   LLM Analysis (Intent + Summary): {'Enabled' if classification_config.enable_llm_analysis else 'Disabled'}"
        )

        # Set up system
        orchestrator, repository, embedder = await setup_system(settings)

        # Demonstrate data processing
        data_success = await demo_data_processing(repository, embedder)

        # Demonstrate classification workflow
        if data_success:
            classification_success = await demo_classification_workflow(orchestrator)

            if classification_success:
                logger.info("\n✨ System demonstration completed successfully!")
                logger.info(
                    "   For more detailed examples, run: uv run demo_classification_orchestrator.py"
                )
            else:
                logger.warning("⚠️  Classification demonstration had issues")
        else:
            logger.warning(
                "⚠️  Data processing check failed - classification may not work properly"
            )

        print("\n" + "=" * 80)
        print("🎉 Application execution completed!")
        print("=" * 80)

        return 0

    except Exception as e:
        logger.error(f"❌ Application failed: {e}")
        print(f"\n❌ Application failed: {e}")
        return 1


if __name__ == "__main__":
    # Run the application
    load_dotenv()
    exit_code = asyncio.run(main())
    exit(exit_code)
