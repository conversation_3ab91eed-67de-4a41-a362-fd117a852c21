"""Application layer use case for intent detection and content summarization."""

import statistics
from collections import defaultdict
from typing import Any, Dict

from src.domain.entities.intent_detection_request import IntentDetectionRequest
from src.domain.entities.intent_detection_result import IntentDetectionResult
from src.domain.exceptions.intent_detection_exceptions import (
    InsufficientContentError,
)
from src.infrastructure.intent_analysis.agno_intent_analyzer import (
    AgnoIntentAnalyzer,
    IntentDetectionConfig,
)


class IntentDetectionMetrics:
    """
    Comprehensive metrics collection for intent detection and summarization operations.
    Tracks performance for dual-task processing.
    """

    def __init__(self):
        self.processing_times = []
        self.intent_frequencies = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.model_usage = defaultdict(int)  # Track primary vs fallback usage
        self.summary_lengths = []  # Track summary quality metrics
        self.dual_task_success_rate = []  # Track successful dual-task completion

    def record_successful_analysis(self, result: IntentDetectionResult):
        """Record metrics for successful intent detection and summarization."""

        # Processing time
        self.processing_times.append(result.processing_time_ms)

        # Intent frequency tracking
        for intent in result.detected_intents:
            self.intent_frequencies[intent.intent_name] += 1

        # Summary quality metrics
        if result.content_summary:
            self.summary_lengths.append(len(result.content_summary))

        # Dual-task success tracking
        has_intents = len(result.detected_intents) > 0
        has_summary = bool(result.content_summary.strip())
        dual_task_success = has_intents and has_summary
        self.dual_task_success_rate.append(1 if dual_task_success else 0)

    def record_model_usage(self, model_name: str):
        """Record which model was used for analysis."""
        self.model_usage[model_name] += 1

    def record_error(self, error_type: str):
        """Record error occurrence by type."""
        self.error_counts[error_type] += 1

    def get_performance_summary(self) -> Dict[str, Any]:
        """Generate performance summary for monitoring."""

        if not self.processing_times:
            return {"status": "no_data"}

        return {
            "total_analyses": len(self.processing_times),
            "avg_processing_time_ms": statistics.mean(self.processing_times),
            "most_common_intents": dict(
                sorted(
                    self.intent_frequencies.items(), key=lambda x: x[1], reverse=True
                )[:5]
            ),
            "model_usage_distribution": dict(self.model_usage),
            "error_distribution": dict(self.error_counts),
            "avg_summary_length": statistics.mean(self.summary_lengths)
            if self.summary_lengths
            else 0,
            "dual_task_success_rate": statistics.mean(self.dual_task_success_rate)
            if self.dual_task_success_rate
            else 0,
        }


class DetectIntentsUseCase:
    """
    Application layer use case for intent detection and content summarization using Agno framework.
    Performs dual tasks to support enhanced vector similarity matching.
    """

    def __init__(self, config: IntentDetectionConfig):
        self.config = config
        self.intent_analyzer = AgnoIntentAnalyzer(config)
        self.metrics = IntentDetectionMetrics()

    async def execute(self, request: IntentDetectionRequest) -> IntentDetectionResult:
        """
        Main execution method for dual-task processing: intent detection + content summarization.

        Args:
            request: Validated intent detection request

        Returns:
            IntentDetectionResult with all detected intents and content summary

        Raises:
            IntentDetectionError: For unrecoverable errors
        """

        try:
            # Input validation
            self._validate_request(request)

            # Perform intent analysis
            result = await self._perform_intent_analysis(request)

            # Record metrics
            self.metrics.record_successful_analysis(result)

            return result

        except Exception as e:
            # Handle errors with fallback strategies
            self.metrics.record_error(type(e).__name__)
            return await self._handle_analysis_error(request, e)

    def _validate_request(self, request: IntentDetectionRequest):
        """Validate the intent detection request."""

        if not request.complaint_content.strip():
            raise InsufficientContentError("陳情內容不能為空")

        if len(request.complaint_content.strip()) < self.config.min_content_length:
            raise InsufficientContentError(
                f"陳情內容過短，至少需要 {self.config.min_content_length} 個字符"
            )

    async def _perform_intent_analysis(
        self, request: IntentDetectionRequest
    ) -> IntentDetectionResult:
        """Core dual-task analysis logic using Agno framework."""
        return await self.intent_analyzer.analyze_intents(request)

    async def _handle_analysis_error(
        self, request: IntentDetectionRequest, error: Exception
    ) -> IntentDetectionResult:
        """
        Implements fallback strategies for different error types.
        """

        if isinstance(error, InsufficientContentError):
            # For very short content, use original content as summary
            return IntentDetectionResult(
                complaint_id=request.complaint_id,
                detected_intents=[],
                content_summary=request.complaint_content
                if len(request.complaint_content) > 10
                else "內容過短無法摘要",
                processing_time_ms=50,
                error_message="內容過短，無法識別意圖",
            )

        else:
            # Generic error handling
            return IntentDetectionResult(
                complaint_id=request.complaint_id,
                detected_intents=[],
                content_summary="",
                processing_time_ms=0,
                error_message=f"意圖檢測失敗: {str(error)[:200]}",
            )

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get performance metrics for monitoring."""
        return self.metrics.get_performance_summary()
