"""Configuration for LLM-based classification service."""

from typing import Optional

from pydantic import Field, field_validator

from .base_llm_config import BaseLLMConfig, SimilarityConfig
from .settings import APIProvider


class ClassificationConfig(BaseLLMConfig):
    """Configuration for LLM-based classification service.

    This configuration is now created through the ConfigurationManager to ensure
    centralized API key management and avoid configuration duplication.
    """

    # Feature Flags
    enable_llm_analysis: bool = Field(default=False)

    # LLM Provider Settings
    llm_provider: str = Field(
        default="google", description="LLM provider: openai, google, anthropic"
    )

    # Model mappings for each provider (overrides parent with specific token limit)
    max_tokens: int = Field(default=500, ge=100, le=2000)

    # Classification Parameters
    similarity_config: SimilarityConfig = Field(default_factory=SimilarityConfig)

    # LLM Settings reference (will be set by ConfigurationManager)
    llm_settings: Optional[object] = Field(default=None, exclude=True)

    @field_validator("llm_provider")
    def validate_llm_provider(cls, v):
        if not APIProvider.is_valid_provider(v):
            valid_providers = APIProvider.get_all_providers()
            raise ValueError(f"llm_provider must be one of: {valid_providers}")
        return v

    def get_current_model_id(self) -> str:
        """Get the current model ID based on selected provider."""
        if self.llm_settings:
            try:
                provider_enum = APIProvider(self.llm_provider)
                return self.llm_settings.get_default_model(provider_enum)
            except ValueError:
                pass

        # Fallback to default mapping
        default_models = {
            "openai": "gpt-4.1-mini",
            "google": "gemini-2.5-flash",
            "anthropic": "claude-3-sonnet-20240229",
        }
        return default_models.get(self.llm_provider, "gemini-2.5-flash")

    def get_api_key_for_provider(self) -> Optional[str]:
        """Get API key for current provider."""
        if self.llm_settings:
            try:
                provider_enum = APIProvider(self.llm_provider)
                return self.llm_settings.get_api_key(provider_enum)
            except ValueError:
                return None
        return None

    def get_provider_config(self) -> Optional[object]:
        """Get provider configuration."""
        if self.llm_settings:
            try:
                provider_enum = APIProvider(self.llm_provider)
                return self.llm_settings.get_provider_config(provider_enum)
            except ValueError:
                return None
        return None

    def is_provider_available(self) -> bool:
        """Check if the current provider is available."""
        if self.llm_settings:
            try:
                provider_enum = APIProvider(self.llm_provider)
                return self.llm_settings.is_provider_available(provider_enum)
            except ValueError:
                return False
        return False

    def get_primary_similarity_threshold(self) -> float:
        """Get primary similarity threshold."""
        return self.similarity_config.primary_similarity_threshold

    def get_fallback_similarity_threshold(self) -> float:
        """Get fallback similarity threshold."""
        return self.similarity_config.fallback_similarity_threshold
