"""Configuration factory for creating service-specific configurations."""

from typing import Any, Dict, Optional, Type, TypeVar
from abc import ABC, abstractmethod

from .settings import Settings, APIProvider
from .base_llm_config import BaseLLMConfig

T = TypeVar('T', bound=BaseLLMConfig)


class ServiceConfigFactory(ABC):
    """Abstract factory for creating service configurations."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
    
    @abstractmethod
    def create_config(self, **overrides) -> BaseLLMConfig:
        """Create configuration for the service."""
        pass


class IntentDetectionConfigFactory(ServiceConfigFactory):
    """Factory for creating intent detection configurations."""
    
    def create_config(self, **overrides) -> 'IntentDetectionConfig':
        """Create intent detection configuration with centralized LLM settings."""
        from .intent_detection_config import IntentDetectionConfig
        
        # Get service-specific config from settings
        service_config = self.settings.get_llm_config_for_service("intent_detection")
        
        # Get API keys for primary and fallback providers
        primary_provider = service_config.get("primary_provider", APIProvider.GOOGLE)
        fallback_provider = service_config.get("fallback_provider", APIProvider.OPENAI)
        
        primary_api_key = self.settings.llm.get_api_key(primary_provider)
        fallback_api_key = self.settings.llm.get_api_key(fallback_provider)
        
        # Create configuration with defaults and overrides
        config_data = {
            "primary_api_key": primary_api_key,
            "fallback_api_key": fallback_api_key,
            "primary_model": self.settings.llm.get_default_model(primary_provider),
            "fallback_model": self.settings.llm.get_default_model(fallback_provider),
            "max_tokens": service_config.get("max_tokens", 4096),
            **overrides
        }
        
        return IntentDetectionConfig(**config_data)


class ClassificationConfigFactory(ServiceConfigFactory):
    """Factory for creating classification configurations."""
    
    def create_config(self, **overrides) -> 'ClassificationConfig':
        """Create classification configuration with centralized LLM settings."""
        from .classification_config import ClassificationConfig
        
        # Get service-specific config from settings
        service_config = self.settings.get_llm_config_for_service("classification")
        
        # Create configuration with defaults and overrides
        config_data = {
            "llm_provider": service_config.get("primary_provider", APIProvider.GOOGLE).value,
            "max_tokens": service_config.get("max_tokens", 500),
            **overrides
        }
        
        config = ClassificationConfig(**config_data)
        
        # Set the centralized LLM settings reference
        config.llm_settings = self.settings.llm
        
        return config


class ConfigurationManager:
    """Central configuration manager that provides unified access to all configurations."""
    
    def __init__(self, settings: Optional[Settings] = None):
        self.settings = settings or Settings()
        self._factories = {
            "intent_detection": IntentDetectionConfigFactory(self.settings),
            "classification": ClassificationConfigFactory(self.settings)
        }
        self._cached_configs: Dict[str, Any] = {}
    
    def get_intent_detection_config(self, **overrides) -> 'IntentDetectionConfig':
        """Get intent detection configuration."""
        cache_key = f"intent_detection_{hash(frozenset(overrides.items()))}"
        
        if cache_key not in self._cached_configs:
            factory = self._factories["intent_detection"]
            self._cached_configs[cache_key] = factory.create_config(**overrides)
        
        return self._cached_configs[cache_key]
    
    def get_classification_config(self, **overrides) -> 'ClassificationConfig':
        """Get classification configuration."""
        cache_key = f"classification_{hash(frozenset(overrides.items()))}"
        
        if cache_key not in self._cached_configs:
            factory = self._factories["classification"]
            self._cached_configs[cache_key] = factory.create_config(**overrides)
        
        return self._cached_configs[cache_key]
    
    def get_llm_settings(self) -> 'LLMSettings':
        """Get centralized LLM settings."""
        return self.settings.llm
    
    def get_available_providers(self) -> list[APIProvider]:
        """Get list of available LLM providers."""
        return self.settings.llm.get_available_providers()
    
    def is_provider_available(self, provider: APIProvider) -> bool:
        """Check if a provider is available."""
        return self.settings.llm.is_provider_available(provider)
    
    def clear_cache(self) -> None:
        """Clear configuration cache."""
        self._cached_configs.clear()
    
    def reload_settings(self) -> None:
        """Reload settings and clear cache."""
        self.settings = Settings()
        self._factories = {
            "intent_detection": IntentDetectionConfigFactory(self.settings),
            "classification": ClassificationConfigFactory(self.settings)
        }
        self.clear_cache()


# Global configuration manager instance
_config_manager: Optional[ConfigurationManager] = None


def get_config_manager() -> ConfigurationManager:
    """Get the global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager()
    return _config_manager


def reset_config_manager() -> None:
    """Reset the global configuration manager (useful for testing)."""
    global _config_manager
    _config_manager = None


# Convenience functions for backward compatibility
def get_intent_detection_config(**overrides) -> 'IntentDetectionConfig':
    """Get intent detection configuration."""
    return get_config_manager().get_intent_detection_config(**overrides)


def get_classification_config(**overrides) -> 'ClassificationConfig':
    """Get classification configuration."""
    return get_config_manager().get_classification_config(**overrides)


def get_llm_settings() -> 'LLMSettings':
    """Get centralized LLM settings."""
    return get_config_manager().get_llm_settings()
