"""Agno-based intent analyzer for complaint text analysis."""

import logging
import time
from typing import List, Optional

from agno.agent import Agent
from agno.models.google import Gemini
from agno.models.openai import OpenAIChat

from src.config.intent_detection_config import IntentDetectionConfig
from src.domain.entities.detected_intent import DetectedIntent
from src.domain.entities.intent_detection_request import IntentDetectionRequest
from src.domain.entities.intent_detection_result import IntentDetectionResult
from src.domain.entities.intent_type import IntentType
from src.infrastructure.models.intent_analysis_models import (
    DetectedIntentModel,
    IntentAnalysisResponse,
)

logger = logging.getLogger(__name__)


class AgnoIntentAnalyzer:
    """
    Analyzes complaint text to detect multiple intents and generate summaries using Agno Agent framework.
    Supports multiple model providers with fallback capabilities.
    Performs dual tasks: intent detection and content summarization.
    """

    def __init__(self, config: IntentDetectionConfig):
        self.config = config

        # Primary agent with Gemini 2.5 Flash
        self.primary_agent = Agent(
            model=Gemini(
                id=config.primary_model,
                temperature=config.temperature,
                # max_output_tokens=config.max_tokens,
                api_key=config.primary_api_key,
            ),
            # tools=[ReasoningTools(add_instructions=True)],
            instructions=self._get_agent_instructions(),
            response_model=IntentAnalysisResponse,
            structured_outputs=True,
            markdown=False,
            telemetry=False,  # 關閉最小化遙測
            monitoring=False,  # 關閉監控上報
        )

        # Fallback agent with GPT-4.1 Mini
        self.fallback_agent = Agent(
            model=OpenAIChat(
                id=config.fallback_model,
                temperature=config.temperature,
                # max_tokens=config.max_tokens,
                api_key=config.fallback_api_key,
            ),
            # tools=[ReasoningTools(add_instructions=True)],
            instructions=self._get_agent_instructions(),
            response_model=IntentAnalysisResponse,
            structured_outputs=True,
            markdown=False,
            telemetry=False,  # 關閉最小化遙測
            monitoring=False,  # 關閉監控上報
        )

    def _get_agent_instructions(self) -> str:
        """
        Comprehensive instructions for the Agno agent to perform dual tasks: intent detection and content summarization.
        Optimized for Traditional Chinese complaint analysis with noise filtering.
        Uses IntentType enum to dynamically generate predefined intent categories.
        """

        # Generate predefined intent list dynamically from IntentType enum
        predefined_intents = IntentType.get_all_intents()

        # Create formatted intent list with descriptions
        intent_descriptions = {
            IntentType.REPORT_VIOLATION.value: "舉報違法或不當行為",
            IntentType.REQUEST_ASSISTANCE.value: "尋求政府單位協助解決問題",
            IntentType.COMPLAINT_PROBLEM.value: "對政府服務或政策表達不滿",
            IntentType.REQUEST_IMPROVEMENT.value: "建議改善現有服務或制度",
            IntentType.CONSULTATION_INQUIRY.value: "詢問相關法規或程序",
            IntentType.DISSATISFIED_SERVICE_ATTITUDE.value: "對承辦人員態度不滿",
            IntentType.DISSATISFIED_STAFF_PROFESSIONALISM.value: "對承辦人員專業能力不滿",
            IntentType.GRATITUDE_PRAISE.value: "表達感謝或肯定",
            IntentType.SUGGEST_PROPOSAL.value: "提供建設性意見",
            IntentType.OTHER.value: "其他意圖類別",
        }

        # Build intent list string dynamically
        intent_list = "\n".join(
            [
                f"    {i + 1}. {intent} - {intent_descriptions.get(intent, '相關意圖')}"
                for i, intent in enumerate(predefined_intents)
            ]
        )

        return f"""
        你是專門分析台灣政府民眾陳情的AI助理。你的任務是執行兩項核心工作：
        1. 識別陳情內容中的所有相關意圖類別
        2. 生成簡潔的重點摘要，過濾雜訊內容

        ## 預設意圖類別（優先使用）：
{intent_list}

        ## 意圖分析原則：
        - 可同時識別多個意圖，不限制數量
        - 優先使用預設類別，若預設類別無法準確描述意圖，可提供更適合的自定義意圖名稱
        - 識別所有相關意圖，不考慮意圖間的衝突
        - 為每個識別出的意圖提供清楚的推理說明
        - 列出支持該意圖的具體文字證據

        ## 內容摘要原則：
        - 摘要長度控制在100-300個繁體中文字
        - 過濾掉無關的個人資訊、重複表達、情緒性詞彙等雜訊
        - 保留核心問題、具體事實、訴求重點
        - 使用適合政府行政處理的正式用語
        - 確保摘要內容與意圖分析一致

        ## 輸出要求：
        - 識別出的意圖列表，每個意圖包含名稱、推理說明和文字證據
        - 生成100-300字的內容重點摘要
        - 所有輸出將自動格式化為結構化數據，無需手動JSON格式化
        """

    async def analyze_intents(
        self, request: IntentDetectionRequest
    ) -> IntentDetectionResult:
        """
        Main entry point for dual-task analysis (intent detection + summarization) using Agno agents.
        """

        try:
            # Primary analysis with Gemini 2.5 Flash
            result = await self._analyze_with_agent(self.primary_agent, request)
            return result

        except Exception as primary_error:
            # Fallback to GPT-4.1 Mini
            try:
                result = await self._analyze_with_agent(self.fallback_agent, request)
                return result

            except Exception as fallback_error:
                # Return error result if both models fail
                return self._create_error_result(request, primary_error, fallback_error)

    async def _analyze_with_agent(
        self, agent: Agent, request: IntentDetectionRequest
    ) -> IntentDetectionResult:
        """
        Performs intent analysis using the specified Agno agent.
        """

        # Construct dual-task analysis prompt
        analysis_prompt = f"""
        請分析以下台灣政府民眾陳情，執行意圖識別和內容摘要：

        陳情主旨：{request.complaint_subject}
        陳情內容：{request.complaint_content}

        請按照系統指示進行雙重分析：
        1. 識別所有相關意圖類別
        2. 生成簡潔的重點摘要（過濾雜訊）

        回應將自動結構化處理。
        """

        start_time = time.time()

        # Run agent analysis
        response = agent.run(analysis_prompt)

        processing_time = int((time.time() - start_time) * 1000)

        logger.debug(f"Agno agent response: {response.content}")

        # Extract structured response from Agno Agent's RunResponse wrapper
        # analysis_response = self._extract_structured_response(response)
        analysis_response = response.content

        # Transform Pydantic models to domain objects
        detected_intents = self._transform_to_domain_intents(
            analysis_response.detected_intents
        )

        return IntentDetectionResult(
            complaint_id=request.complaint_id,
            detected_intents=detected_intents,
            content_summary=analysis_response.content_summary,
            processing_time_ms=processing_time,
            error_message=None,
        )

    def _transform_to_domain_intents(
        self, intent_models: List[DetectedIntentModel]
    ) -> List[DetectedIntent]:
        """
        Transforms Pydantic intent models into domain objects.
        Uses IntentType enum to verify if intents are predefined.
        """

        detected_intents = []

        for intent_model in intent_models:
            # Use IntentType enum to determine if intent is predefined
            is_predefined = IntentType.is_valid_intent(intent_model.intent_name)

            detected_intent = DetectedIntent(
                intent_name=intent_model.intent_name,
                is_predefined=is_predefined,
                reasoning=intent_model.reasoning,
                text_evidence=intent_model.text_evidence,
            )
            detected_intents.append(detected_intent)

        return detected_intents

    def _create_error_result(
        self,
        request: IntentDetectionRequest,
        primary_error: Exception,
        fallback_error: Optional[Exception] = None,
    ) -> IntentDetectionResult:
        """
        Creates error result when both primary and fallback models fail.
        """

        error_message = f"意圖檢測失敗，主要模型錯誤: {str(primary_error)[:100]}"
        if fallback_error:
            error_message += f", 備用模型錯誤: {str(fallback_error)[:100]}"

        return IntentDetectionResult(
            complaint_id=request.complaint_id,
            detected_intents=[],
            content_summary="",
            processing_time_ms=0,
            error_message=error_message,
        )
