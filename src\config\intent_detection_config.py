"""Configuration for intent detection and summarization engine."""

from typing import Optional

from pydantic import Field

from .base_llm_config import BaseLLMConfig


class IntentDetectionConfig(BaseLLMConfig):
    """Configuration for intent detection and summarization engine using Agno framework.

    This configuration is now created through the ConfigurationManager to ensure
    centralized API key management and avoid configuration duplication.
    """

    # Primary Model Configuration (Gemini 2.5 Flash)
    primary_model: str = Field(default="gemini-2.5-flash")
    primary_api_key: Optional[str] = Field(None)

    # Fallback Model Configuration (GPT-4.1 Mini)
    fallback_model: str = Field(default="gpt-4.1-mini")
    fallback_api_key: Optional[str] = Field(None)

    # Override max_tokens with intent detection specific limit
    max_tokens: int = Field(default=4096, ge=100, le=4096)

    # Performance Configuration is inherited from BaseLLMConfig

    # Fallback Configuration
    min_content_length: int = Field(default=10, ge=5, le=50)

    def get_primary_api_key(self) -> Optional[str]:
        """Get primary API key for intent detection."""
        return self.primary_api_key

    def get_fallback_api_key(self) -> Optional[str]:
        """Get fallback API key for intent detection."""
        return self.fallback_api_key

    def has_valid_primary_key(self) -> bool:
        """Check if primary API key is valid."""
        return self.primary_api_key is not None and self.primary_api_key.strip() != ""

    def has_valid_fallback_key(self) -> bool:
        """Check if fallback API key is valid."""
        return self.fallback_api_key is not None and self.fallback_api_key.strip() != ""
