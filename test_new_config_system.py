#!/usr/bin/env python3
"""
Test script for the new configuration system.

This script validates that the refactored configuration system works correctly
and demonstrates the improvements made.
"""

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from src.config import (
    APIProvider,
    Settings,
    get_classification_config,
    get_intent_detection_config,
    reset_config_manager,
)


def setup_test_environment():
    """Set up test environment variables."""
    # Override environment variables for testing
    os.environ["GEMINI_API_KEY"] = "test-gemini-key"
    os.environ["OPENAI_API_KEY"] = "test-openai-key"
    os.environ["ANTHROPIC_API_KEY"] = "test-anthropic-key"


def test_basic_settings():
    """Test basic settings functionality."""
    print("🔧 Testing basic Settings class...")

    # Create settings with test values
    settings = Settings(
        gemini_api_key_env="test-gemini-key",
        openai_api_key_env="test-openai-key",
        anthropic_api_key_env="test-anthropic-key",
    )

    # Test backward compatibility properties
    assert settings.gemini_api_key == "test-gemini-key"
    assert settings.openai_api_key == "test-openai-key"
    assert settings.anthropic_api_key == "test-anthropic-key"

    # Test LLM settings initialization
    assert settings.llm is not None
    assert settings.llm.get_api_key(APIProvider.GOOGLE) == "test-gemini-key"
    assert settings.llm.get_api_key(APIProvider.OPENAI) == "test-openai-key"
    assert settings.llm.get_api_key(APIProvider.ANTHROPIC) == "test-anthropic-key"

    print("✅ Basic settings test passed!")


def test_configuration_manager():
    """Test the new configuration manager."""
    print("🏭 Testing ConfigurationManager...")

    # Reset to ensure clean state
    reset_config_manager()

    # Create a test settings instance
    from src.config.config_factory import ConfigurationManager

    test_settings = Settings(
        gemini_api_key_env="test-gemini-key",
        openai_api_key_env="test-openai-key",
        anthropic_api_key_env="test-anthropic-key",
    )
    config_manager = ConfigurationManager(test_settings)

    # Test intent detection config
    intent_config = config_manager.get_intent_detection_config()
    assert intent_config is not None
    assert intent_config.primary_api_key == "test-gemini-key"
    assert intent_config.fallback_api_key == "test-openai-key"
    assert intent_config.primary_model == "gemini-2.5-flash"
    assert intent_config.fallback_model == "gpt-4.1-mini"

    # Test classification config
    classification_config = config_manager.get_classification_config()
    assert classification_config is not None
    assert classification_config.llm_provider == "google"
    assert classification_config.llm_settings is not None

    # Test provider availability
    available_providers = config_manager.get_available_providers()
    assert APIProvider.GOOGLE in available_providers
    assert APIProvider.OPENAI in available_providers
    assert APIProvider.ANTHROPIC in available_providers

    print("✅ Configuration manager test passed!")


def test_convenience_functions():
    """Test convenience functions."""
    print("🎯 Testing convenience functions...")

    # Set up global config manager with test settings first
    import src.config.config_factory as factory_module
    from src.config.config_factory import ConfigurationManager

    test_settings = Settings(
        gemini_api_key_env="test-gemini-key",
        openai_api_key_env="test-openai-key",
        anthropic_api_key_env="test-anthropic-key",
    )
    factory_module._config_manager = ConfigurationManager(test_settings)

    # Test direct config access
    intent_config = get_intent_detection_config()
    assert intent_config is not None

    classification_config = get_classification_config()
    assert classification_config is not None

    # Test with overrides
    custom_intent_config = get_intent_detection_config(max_tokens=2048)
    assert custom_intent_config.max_tokens == 2048

    custom_classification_config = get_classification_config(enable_llm_analysis=True)
    assert custom_classification_config.enable_llm_analysis == True

    print("✅ Convenience functions test passed!")


def test_provider_management():
    """Test provider management features."""
    print("🔌 Testing provider management...")

    # Use the test config manager set up in previous test
    import src.config.config_factory as factory_module

    config_manager = factory_module._config_manager
    llm_settings = config_manager.get_llm_settings()

    # Test provider configurations
    google_config = llm_settings.get_provider_config(APIProvider.GOOGLE)
    assert google_config is not None
    assert google_config.default_model == "gemini-2.5-flash"
    assert "gemini-1.5-pro" in google_config.available_models

    openai_config = llm_settings.get_provider_config(APIProvider.OPENAI)
    assert openai_config is not None
    assert openai_config.default_model == "gpt-4.1-mini"

    # Test provider availability
    assert llm_settings.is_provider_available(APIProvider.GOOGLE)
    assert llm_settings.is_provider_available(APIProvider.OPENAI)
    assert llm_settings.is_provider_available(APIProvider.ANTHROPIC)

    print("✅ Provider management test passed!")


def test_configuration_caching():
    """Test configuration caching."""
    print("💾 Testing configuration caching...")

    # Use the test config manager set up in previous test
    import src.config.config_factory as factory_module

    config_manager = factory_module._config_manager

    # Get same config multiple times
    config1 = config_manager.get_intent_detection_config()
    config2 = config_manager.get_intent_detection_config()

    # Should be the same instance due to caching
    assert config1 is config2

    # Clear cache and get again
    config_manager.clear_cache()
    config3 = config_manager.get_intent_detection_config()

    # Should be different instance after cache clear
    assert config1 is not config3

    print("✅ Configuration caching test passed!")


def demonstrate_improvements():
    """Demonstrate the improvements made."""
    print("\n🎉 Configuration System Improvements:")
    print("=" * 60)

    print("✅ Eliminated duplicate API key configurations")
    print("   - Centralized API key management in APIKeyManager")
    print("   - Single source of truth for all API keys")

    print("✅ Resolved circular import issues")
    print("   - Removed dynamic imports in __init__ methods")
    print("   - Clean module structure with proper dependencies")

    print("✅ Improved maintainability and extensibility")
    print("   - Configuration factory pattern for service configs")
    print("   - Easy to add new LLM providers")
    print("   - Centralized configuration management")

    print("✅ Enhanced provider management")
    print("   - Support for multiple LLM providers (Google, OpenAI, Anthropic)")
    print("   - Provider-specific model configurations")
    print("   - Runtime provider availability checking")

    print("✅ Better configuration caching")
    print("   - Cached configurations for performance")
    print("   - Easy cache management and reloading")

    print("✅ Backward compatibility maintained")
    print("   - Existing code continues to work")
    print("   - Gradual migration path available")


def main():
    """Main test function."""
    print("🚀 Testing New Configuration System")
    print("=" * 50)

    try:
        # Set up test environment first
        setup_test_environment()

        test_basic_settings()
        test_configuration_manager()
        test_convenience_functions()
        test_provider_management()
        test_configuration_caching()

        demonstrate_improvements()

        print("\n🎉 All tests passed! Configuration system is working correctly.")
        return 0

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
