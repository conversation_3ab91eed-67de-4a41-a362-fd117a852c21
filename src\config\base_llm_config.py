"""Base configuration for LLM services."""

from pydantic import BaseModel, Field


class BaseLLMConfig(BaseModel):
    """Base configuration for LLM services with common parameters."""
    
    # LLM Parameters
    temperature: float = Field(default=0.1, ge=0.0, le=2.0)
    max_tokens: int = Field(default=1000, ge=100, le=4096)
    
    # Performance Configuration
    max_concurrent_requests: int = Field(default=10, ge=1, le=50)
    request_timeout_seconds: int = Field(default=30, ge=10, le=120)
    enable_retries: bool = Field(default=True)


class SimilarityConfig(BaseModel):
    """Configuration for similarity thresholds."""
    
    primary_similarity_threshold: float = Field(default=0.75, ge=0.0, le=1.0)
    fallback_similarity_threshold: float = Field(default=0.6, ge=0.0, le=1.0)
    
    @classmethod
    def validate_fallback_threshold(cls, v, values):
        """Validate that fallback threshold is lower than primary threshold."""
        if (
            "primary_similarity_threshold" in values
            and v >= values["primary_similarity_threshold"]
        ):
            raise ValueError("Fallback threshold must be lower than primary threshold")
        return v